import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentUserRepository,
  AgentUrlRepository,
  AgentMediaRepository,
  AgentProductRepository
} from '@modules/agent/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { MediaRepository } from '@modules/data/media/repositories';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { UserProduct } from '@modules/business/entities';
import {
  AddAgentUrlDto,
  AddAgentMediaDto,
  AddAgentProductDto,
  RemoveAgentUrlDto,
  RemoveAgentMediaDto,
  RemoveAgentProductDto,
  AgentUrlQueryDto,
  AgentMediaQueryDto,
  AgentProductQueryDto,
  AgentUrlResponseDto,
  AgentMediaResponseDto,
  AgentProductResponseDto
} from '../dto/resource';
import { AgentResourceMapper } from '../mappers';

/**
 * Service xử lý các thao tác liên quan đến tài nguyên của agent cho người dùng
 */
@Injectable()
export class AgentResourceUserService {
  private readonly logger = new Logger(AgentResourceUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentUrlRepository: AgentUrlRepository,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly agentProductRepository: AgentProductRepository,
    private readonly urlRepository: UrlRepository,
    private readonly mediaRepository: MediaRepository,
    private readonly userProductRepository: Repository<UserProduct>,
  ) { }

  // ==================== URL METHODS ====================

  /**
   * Lấy danh sách URL của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách URL có phân trang
   */
  async getAgentUrls(
    agentId: string,
    userId: number,
    queryDto: AgentUrlQueryDto,
  ): Promise<PaginatedResult<AgentUrlResponseDto>> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Lấy danh sách URL IDs từ agent_url
      const agentUrls = await this.agentUrlRepository.findByAgentId(agentId);
      const urlIds = agentUrls.map(au => au.urlId);

      if (urlIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết URL từ url_data với phân trang
      const urls = await this.urlRepository.findUrlsByIds(urlIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      ));

      const total = urlIds.length;
      const items = AgentResourceMapper.toUrlResponseDtos(urls);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách URL của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm URL vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin URL cần thêm
   */
  async addAgentUrls(
    agentId: string,
    userId: number,
    addDto: AddAgentUrlDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra các URL có tồn tại và thuộc về user không
      await this.validateUrlOwnership(addDto.urlIds, userId);

      // Thêm từng URL vào agent (sử dụng repository từ agent-resource.repository.ts)
      for (const urlId of addDto.urlIds) {
        // Kiểm tra URL đã được thêm vào agent chưa
        const existingAgentUrl = await this.agentUrlRepository.findOne({
          where: { agentId, urlId },
        });

        if (!existingAgentUrl) {
          // Thêm URL vào agent
          const agentUrl = this.agentUrlRepository.create({
            agentId,
            urlId,
          });
          await this.agentUrlRepository.save(agentUrl);
        }
      }

      this.logger.log(`Đã thêm ${addDto.urlIds.length} URL vào agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi thêm URL vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ URL khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin URL cần gỡ bỏ
   */
  async removeAgentUrls(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentUrlDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Gỡ bỏ từng URL khỏi agent
      for (const urlId of removeDto.urlIds) {
        const result = await this.agentUrlRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('urlId = :urlId', { urlId })
          .execute();

        if (result.affected === 0) {
          throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
        }
      }

      this.logger.log(`Đã gỡ bỏ ${removeDto.urlIds.length} URL khỏi agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ URL khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== MEDIA METHODS ====================

  /**
   * Lấy danh sách Media của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Media có phân trang
   */
  async getAgentMedias(
    agentId: string,
    userId: number,
    queryDto: AgentMediaQueryDto,
  ): Promise<PaginatedResult<AgentMediaResponseDto>> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Lấy danh sách Media IDs từ agents_media
      const agentMedias = await this.agentMediaRepository.findByAgentId(agentId);
      const mediaIds = agentMedias.map(am => am.mediaId);

      if (mediaIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết Media từ media_data với phân trang
      const medias = await this.mediaRepository.findByIds(mediaIds.slice(
        (queryDto.page - 1) * queryDto.limit,
        queryDto.page * queryDto.limit
      ));

      const total = mediaIds.length;
      const items = AgentResourceMapper.toMediaResponseDtos(medias);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Media của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm Media vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin Media cần thêm
   */
  async addAgentMedias(
    agentId: string,
    userId: number,
    addDto: AddAgentMediaDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra các Media có tồn tại và thuộc về user không
      await this.validateMediaOwnership(addDto.mediaIds, userId);

      // Thêm từng Media vào agent
      for (const mediaId of addDto.mediaIds) {
        // Kiểm tra Media đã được thêm vào agent chưa
        const existingAgentMedia = await this.agentMediaRepository.findOne({
          where: { agentId, mediaId },
        });

        if (!existingAgentMedia) {
          // Thêm Media vào agent
          const agentMedia = this.agentMediaRepository.create({
            agentId,
            mediaId,
          });
          await this.agentMediaRepository.save(agentMedia);
        }
      }

      this.logger.log(`Đã thêm ${addDto.mediaIds.length} Media vào agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Media vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ Media khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin Media cần gỡ bỏ
   */
  async removeAgentMedias(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentMediaDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Gỡ bỏ từng Media khỏi agent
      for (const mediaId of removeDto.mediaIds) {
        const result = await this.agentMediaRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('mediaId = :mediaId', { mediaId })
          .execute();

        if (result.affected === 0) {
          throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
        }
      }

      this.logger.log(`Đã gỡ bỏ ${removeDto.mediaIds.length} Media khỏi agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Media khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== PRODUCT METHODS ====================

  /**
   * Lấy danh sách Product của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Product có phân trang
   */
  async getAgentProducts(
    agentId: string,
    userId: number,
    queryDto: AgentProductQueryDto,
  ): Promise<PaginatedResult<AgentProductResponseDto>> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Lấy danh sách Product IDs từ agents_product
      const agentProducts = await this.agentProductRepository.findByAgentId(agentId);
      const productIds = agentProducts.map(ap => ap.productId);

      if (productIds.length === 0) {
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: queryDto.limit,
            totalPages: 0,
            currentPage: queryDto.page,
          },
        };
      }

      // Lấy thông tin chi tiết Product từ user_products với phân trang
      const products = await this.userProductRepository.find({
        where: { id: In(productIds.slice(
          (queryDto.page - 1) * queryDto.limit,
          queryDto.page * queryDto.limit
        )) },
        order: { createdAt: 'DESC' }
      });

      const total = productIds.length;
      const items = AgentResourceMapper.toProductResponseDtos(products);

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách Product của agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Thêm Product vào agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param addDto Thông tin Product cần thêm
   */
  async addAgentProducts(
    agentId: string,
    userId: number,
    addDto: AddAgentProductDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra các Product có tồn tại và thuộc về user không
      await this.validateProductOwnership(addDto.productIds, userId);

      // Thêm từng Product vào agent
      for (const productId of addDto.productIds) {
        // Kiểm tra Product đã được thêm vào agent chưa
        const existingAgentProduct = await this.agentProductRepository.findOne({
          where: { agentId, productId },
        });

        if (!existingAgentProduct) {
          // Thêm Product vào agent
          const agentProduct = this.agentProductRepository.create({
            agentId,
            productId,
          });
          await this.agentProductRepository.save(agentProduct);
        }
      }

      this.logger.log(`Đã thêm ${addDto.productIds.length} Product vào agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi thêm Product vào agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Gỡ bỏ Product khỏi agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param removeDto Thông tin Product cần gỡ bỏ
   */
  async removeAgentProducts(
    agentId: string,
    userId: number,
    removeDto: RemoveAgentProductDto,
  ): Promise<void> {
    try {
      // Kiểm tra agent có thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Gỡ bỏ từng Product khỏi agent
      for (const productId of removeDto.productIds) {
        const result = await this.agentProductRepository.createQueryBuilder()
          .delete()
          .where('agentId = :agentId', { agentId })
          .andWhere('productId = :productId', { productId })
          .execute();

        if (result.affected === 0) {
          throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
        }
      }

      this.logger.log(`Đã gỡ bỏ ${removeDto.productIds.length} Product khỏi agent ${agentId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi gỡ bỏ Product khỏi agent: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== VALIDATION METHODS ====================

  /**
   * Kiểm tra Agent có tồn tại và thuộc về user không
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   */
  private async checkAgentOwnership(agentId: string, userId: number): Promise<void> {
    const agentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

    if (!agentUser) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
  }

  /**
   * Kiểm tra các URL có tồn tại và thuộc về user không
   * @param urlIds Danh sách ID của URL
   * @param userId ID của người dùng
   */
  private async validateUrlOwnership(urlIds: string[], userId: number): Promise<void> {
    for (const urlId of urlIds) {
      const url = await this.urlRepository.findUrlById(urlId);
      if (!url || url.ownedBy !== userId) {
        throw new AppException(AGENT_ERROR_CODES.URL_NOT_FOUND);
      }
    }
  }

  /**
   * Kiểm tra các Media có tồn tại và thuộc về user không
   * @param mediaIds Danh sách ID của Media
   * @param userId ID của người dùng
   */
  private async validateMediaOwnership(mediaIds: string[], userId: number): Promise<void> {
    for (const mediaId of mediaIds) {
      const media = await this.mediaRepository.findByIdConfig(mediaId);
      if (!media || media.ownedBy !== userId) {
        throw new AppException(AGENT_ERROR_CODES.MEDIA_NOT_FOUND);
      }
    }
  }

  /**
   * Kiểm tra các Product có tồn tại và thuộc về user không
   * @param productIds Danh sách ID của Product
   * @param userId ID của người dùng
   */
  private async validateProductOwnership(productIds: number[], userId: number): Promise<void> {
    for (const productId of productIds) {
      const product = await this.userProductRepository.findOne({
        where: { id: productId, createdBy: userId }
      });
      if (!product) {
        throw new AppException(AGENT_ERROR_CODES.PRODUCT_NOT_FOUND);
      }
    }
  }
}