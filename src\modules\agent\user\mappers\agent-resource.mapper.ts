import { Url } from '@modules/data/url/entities';
import { Media } from '@modules/data/media/entities';
import { UserProduct } from '@modules/business/entities';
import { 
  AgentUrlResponseDto, 
  AgentMediaResponseDto, 
  AgentProductResponseDto 
} from '../dto/resource';

/**
 * Mapper cho các tài nguyên của agent
 */
export class AgentResourceMapper {
  /**
   * Chuyển đổi Url entity sang AgentUrlResponseDto
   * @param url Url entity
   * @returns AgentUrlResponseDto
   */
  static toUrlResponseDto(url: Url): AgentUrlResponseDto {
    return {
      id: url.id,
      url: url.url,
      title: url.title,
      createdAt: url.createdAt,
    };
  }

  /**
   * Chuyển đổi danh sách Url entity sang danh sách AgentUrlResponseDto
   * @param urls Danh sách Url entity
   * @returns Danh sách AgentUrlResponseDto
   */
  static toUrlResponseDtos(urls: Url[]): AgentUrlResponseDto[] {
    return urls.map(url => this.toUrlResponseDto(url));
  }

  /**
   * Chuyển đổi Media entity sang AgentMediaResponseDto
   * @param media Media entity
   * @returns AgentMediaResponseDto
   */
  static toMediaResponseDto(media: Media): AgentMediaResponseDto {
    return {
      id: media.id,
      name: media.name,
      description: media.description,
      size: media.size,
      storageKey: media.storageKey,
      createdAt: media.createdAt,
    };
  }

  /**
   * Chuyển đổi danh sách Media entity sang danh sách AgentMediaResponseDto
   * @param medias Danh sách Media entity
   * @returns Danh sách AgentMediaResponseDto
   */
  static toMediaResponseDtos(medias: Media[]): AgentMediaResponseDto[] {
    return medias.map(media => this.toMediaResponseDto(media));
  }

  /**
   * Chuyển đổi UserProduct entity sang AgentProductResponseDto
   * @param product UserProduct entity
   * @returns AgentProductResponseDto
   */
  static toProductResponseDto(product: UserProduct): AgentProductResponseDto {
    // Lấy URL hình ảnh đầu tiên từ trường images (JSONB)
    let imageUrl = '';
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      imageUrl = product.images[0].url || product.images[0].key || '';
    }

    return {
      id: product.id,
      name: product.name,
      imageUrl,
      createdAt: product.createdAt,
    };
  }

  /**
   * Chuyển đổi danh sách UserProduct entity sang danh sách AgentProductResponseDto
   * @param products Danh sách UserProduct entity
   * @returns Danh sách AgentProductResponseDto
   */
  static toProductResponseDtos(products: UserProduct[]): AgentProductResponseDto[] {
    return products.map(product => this.toProductResponseDto(product));
  }
}
