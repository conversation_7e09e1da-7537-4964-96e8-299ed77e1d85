import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  AgentUserRepository,
  AgentRepository
} from '@modules/agent/repositories';
import { CdnService } from '@shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum } from '@shared/utils';
import { Transactional } from 'typeorm-transactional';
import { 
  UpdateBasicInfoDto,
  BasicInfoResponseDto
} from '../dto/basic-info';
import { BasicInfoMapper } from '../mappers';

/**
 * Service xử lý các thao tác liên quan đến basic info của agent cho người dùng
 */
@Injectable()
export class BasicInfoUserService {
  private readonly logger = new Logger(BasicInfoUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentRepository: AgentRepository,
    private readonly cdnService: CdnService,
    private readonly dataSource: DataSource,
  ) { }

  /**
   * Lấy thông tin basic info của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin basic info
   */
  async getBasicInfo(
    agentId: string,
    userId: number,
  ): Promise<BasicInfoResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không và lấy thông tin model
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agent, agentUser } = result;

      // Resolve model information với actual queries
      let resolvedModelId = '';
      let resolvedProvider = 'unknown';
      let keyLlmId: string | null = null;

      if (agentUser.userModelId) {
        // Priority 1: User Model - lấy từ user_models table
        try {
          const userModel = await this.dataSource
            .createQueryBuilder()
            .select(['um.model_id', 'um.key_llm_id'])
            .from('user_models', 'um')
            .where('um.id = :userModelId', { userModelId: agentUser.userModelId })
            .getRawOne();

          if (userModel) {
            resolvedModelId = userModel.model_id || '';
            resolvedProvider = 'USER_KEY';
            keyLlmId = userModel.key_llm_id || null; // keyLlmId chỉ có khi có userModelId
          }
        } catch (error) {
          this.logger.warn(`Không thể resolve user model ${agentUser.userModelId}: ${error.message}`);
        }
      } else if (agentUser.systemModelId) {
        // Priority 2: System Model - lấy từ system_models table
        try {
          const systemModel = await this.dataSource
            .createQueryBuilder()
            .select(['sm.model_id'])
            .from('system_models', 'sm')
            .where('sm.id = :systemModelId', { systemModelId: agentUser.systemModelId })
            .getRawOne();

          if (systemModel) {
            resolvedModelId = systemModel.model_id || '';
            resolvedProvider = 'SYSTEM_KEY';
            // keyLlmId = null (chỉ có khi có userModelId)
          }
        } catch (error) {
          this.logger.warn(`Không thể resolve system model ${agentUser.systemModelId}: ${error.message}`);
        }
      } else if (agentUser.modelFineTuneId) {
        // Priority 3: Fine-tune Model - lấy từ user_model_fine_tune table
        try {
          const fineTuneModel = await this.dataSource
            .createQueryBuilder()
            .select(['umft.model_id'])
            .from('user_model_fine_tune', 'umft')
            .where('umft.id = :modelFineTuneId', { modelFineTuneId: agentUser.modelFineTuneId })
            .getRawOne();

          if (fineTuneModel) {
            resolvedModelId = fineTuneModel.model_id || '';
            resolvedProvider = 'FINE_TUNED';
            // keyLlmId = null (chỉ có khi có userModelId)
          }
        } catch (error) {
          this.logger.warn(`Không thể resolve fine-tune model ${agentUser.modelFineTuneId}: ${error.message}`);
        }
      }

      // Chuyển đổi sang response DTO
      const response = BasicInfoMapper.toResponseDto(
        agent,
        resolvedModelId,
        resolvedProvider,
        keyLlmId || undefined, // Convert null to undefined
        this.cdnService
      );

      this.logger.log(`Lấy basic info thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy basic info agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật basic info của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin basic info cần cập nhật
   * @returns Thông tin basic info đã cập nhật và avatar upload URL
   */
  @Transactional()
  async updateBasicInfo(
    agentId: string,
    userId: number,
    updateDto: UpdateBasicInfoDto,
  ): Promise<BasicInfoResponseDto & { avatarUpload?: { uploadUrl: string; publicUrl: string } }> {
    try {
      // Kiểm tra agent có thuộc về user không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agent, agentUser } = result;

      // Prepare update data for agents table
      const agentUpdateData: any = {};
      let avatarUploadInfo: { uploadUrl: string; publicUrl: string } | null = null;

      // Handle name update
      if (updateDto.name !== undefined) {
        agentUpdateData.name = updateDto.name;
      }

      // Handle avatar upload
      if (updateDto.avatarFile) {
        try {
          const { fileName, mimeType } = updateDto.avatarFile;

          const s3Key = generateS3Key({
            baseFolder: userId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT,
            fileName: fileName,
            useTimeFolder: false
          });

          // TODO: Implement upload URL generation with S3Service
          // const uploadUrl = await this.s3Service.createPresignedUrl(s3Key, mimeType);
          const uploadUrl = `https://upload-placeholder.com/${s3Key}`;
          const publicUrl = `https://cdn-placeholder.com/${s3Key}`;

          agentUpdateData.avatar = s3Key;
          avatarUploadInfo = { uploadUrl, publicUrl };
        } catch (error) {
          this.logger.error(`Lỗi khi tạo S3 key cho avatar: ${error.message}`);
          throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, 'Không thể tạo URL upload cho avatar');
        }
      }

      // Handle model config update
      if (updateDto.modelConfig) {
        if (!BasicInfoMapper.validateModelConfig(updateDto.modelConfig)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG, 'Cấu hình model không hợp lệ');
        }
        
        agentUpdateData.modelConfig = BasicInfoMapper.mergeModelConfig(
          agent.modelConfig,
          updateDto.modelConfig
        );
      }

      // Handle instruction update
      if (updateDto.instruction !== undefined) {
        agentUpdateData.instruction = updateDto.instruction;
      }

      // Handle vector store update
      if (updateDto.vectorStoreId !== undefined) {
        agentUpdateData.vectorStoreId = updateDto.vectorStoreId;
      }

      // Update agents table if there are changes
      if (Object.keys(agentUpdateData).length > 0) {
        await this.agentRepository.createQueryBuilder()
          .update()
          .set(agentUpdateData)
          .where('id = :id', { id: agentId })
          .execute();
      }

      // Prepare update data for agents_user table
      const agentUserUpdateData: any = {};

      // Handle model updates (only one should be set, others should be null)
      if (updateDto.userModelId !== undefined) {
        agentUserUpdateData.userModelId = updateDto.userModelId;
        agentUserUpdateData.keyLlmId = updateDto.keyLlmId || null;
        agentUserUpdateData.systemModelId = null;
        agentUserUpdateData.modelFineTuneId = null;
      } else if (updateDto.systemModelId !== undefined) {
        agentUserUpdateData.systemModelId = updateDto.systemModelId;
        agentUserUpdateData.userModelId = null;
        agentUserUpdateData.modelFineTuneId = null;
      } else if (updateDto.modelFineTuneId !== undefined) {
        agentUserUpdateData.modelFineTuneId = updateDto.modelFineTuneId;
        agentUserUpdateData.userModelId = null;
        agentUserUpdateData.systemModelId = null;
      }

      // Update agents_user table if there are changes
      if (Object.keys(agentUserUpdateData).length > 0) {
        await this.agentUserRepository.createQueryBuilder()
          .update()
          .set(agentUserUpdateData)
          .where('id = :id', { id: agentId })
          .andWhere('userId = :userId', { userId })
          .execute();
      }

      // Get updated agent info
      const updatedResult = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!updatedResult) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Resolve updated model info với actual queries
      let updatedResolvedModelId = '';
      let updatedResolvedProvider = 'unknown';
      let updatedKeyLlmId: string | null = null;

      if (updatedResult.agentUser.userModelId) {
        // Priority 1: User Model - lấy từ user_models table
        try {
          const userModel = await this.dataSource
            .createQueryBuilder()
            .select(['um.model_id', 'um.key_llm_id'])
            .from('user_models', 'um')
            .where('um.id = :userModelId', { userModelId: updatedResult.agentUser.userModelId })
            .getRawOne();

          if (userModel) {
            updatedResolvedModelId = userModel.model_id || '';
            updatedResolvedProvider = 'USER_KEY';
            updatedKeyLlmId = userModel.key_llm_id || null;
          }
        } catch (error) {
          this.logger.warn(`Không thể resolve updated user model ${updatedResult.agentUser.userModelId}: ${error.message}`);
        }
      } else if (updatedResult.agentUser.systemModelId) {
        // Priority 2: System Model - lấy từ system_models table
        try {
          const systemModel = await this.dataSource
            .createQueryBuilder()
            .select(['sm.model_id'])
            .from('system_models', 'sm')
            .where('sm.id = :systemModelId', { systemModelId: updatedResult.agentUser.systemModelId })
            .getRawOne();

          if (systemModel) {
            updatedResolvedModelId = systemModel.model_id || '';
            updatedResolvedProvider = 'SYSTEM_KEY';
            // updatedKeyLlmId = null (chỉ có khi có userModelId)
          }
        } catch (error) {
          this.logger.warn(`Không thể resolve updated system model ${updatedResult.agentUser.systemModelId}: ${error.message}`);
        }
      } else if (updatedResult.agentUser.modelFineTuneId) {
        // Priority 3: Fine-tune Model - lấy từ user_model_fine_tune table
        try {
          const fineTuneModel = await this.dataSource
            .createQueryBuilder()
            .select(['umft.model_id'])
            .from('user_model_fine_tune', 'umft')
            .where('umft.id = :modelFineTuneId', { modelFineTuneId: updatedResult.agentUser.modelFineTuneId })
            .getRawOne();

          if (fineTuneModel) {
            updatedResolvedModelId = fineTuneModel.model_id || '';
            updatedResolvedProvider = 'FINE_TUNED';
            // updatedKeyLlmId = null (chỉ có khi có userModelId)
          }
        } catch (error) {
          this.logger.warn(`Không thể resolve updated fine-tune model ${updatedResult.agentUser.modelFineTuneId}: ${error.message}`);
        }
      }

      // Create response
      const response = BasicInfoMapper.toResponseDto(
        updatedResult.agent,
        updatedResolvedModelId,
        updatedResolvedProvider,
        updatedKeyLlmId || undefined, // Convert null to undefined
        this.cdnService
      );

      const result_with_upload = {
        ...response,
        ...(avatarUploadInfo && { avatarUpload: avatarUploadInfo })
      };

      this.logger.log(`Cập nhật basic info thành công cho agent ${agentId}`);
      return result_with_upload;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật basic info agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }
}
