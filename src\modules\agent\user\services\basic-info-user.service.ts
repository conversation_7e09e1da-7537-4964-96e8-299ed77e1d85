import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { 
  AgentUserRepository,
  AgentRepository
} from '@modules/agent/repositories';
import { CdnService } from '@shared/services/cdn.service';
import { generateS3Key, CategoryFolderEnum } from '@shared/utils/generators/s3-key-generator.util';
import { TimeIntervalEnum } from '@shared/utils';
import { Transactional } from 'typeorm-transactional';
import { 
  UpdateBasicInfoDto,
  BasicInfoResponseDto
} from '../dto/basic-info';
import { BasicInfoMapper } from '../mappers';

/**
 * Service xử lý các thao tác liên quan đến basic info của agent cho người dùng
 */
@Injectable()
export class BasicInfoUserService {
  private readonly logger = new Logger(BasicInfoUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly agentRepository: AgentRepository,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Lấy thông tin basic info của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin basic info
   */
  async getBasicInfo(
    agentId: string,
    userId: number,
  ): Promise<BasicInfoResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không và lấy thông tin model
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agent, agentUser } = result;

      // Resolve model information (simplified logic)
      let resolvedModelId = '';
      let resolvedProvider = 'unknown';

      if (agentUser.userModelId) {
        resolvedModelId = 'user-model-placeholder'; // TODO: Resolve actual model ID
        resolvedProvider = 'user';
      } else if (agentUser.systemModelId) {
        resolvedModelId = 'system-model-placeholder'; // TODO: Resolve actual model ID
        resolvedProvider = 'system';
      } else if (agentUser.modelFineTuneId) {
        resolvedModelId = 'fine-tune-model-placeholder'; // TODO: Resolve actual model ID
        resolvedProvider = 'fine_tune';
      }
      
      // Get key LLM ID (prioritize user model key)
      let keyLlmId: string | undefined;
      if (agentUser.userModelId) {
        // TODO: Get key LLM ID from user_model_key_llm table
        keyLlmId = 'user-key-placeholder'; // Placeholder for now
      }

      // Chuyển đổi sang response DTO
      const response = BasicInfoMapper.toResponseDto(
        agent,
        resolvedModelId,
        resolvedProvider,
        keyLlmId,
        this.cdnService
      );

      this.logger.log(`Lấy basic info thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy basic info agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật basic info của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin basic info cần cập nhật
   * @returns Thông tin basic info đã cập nhật và avatar upload URL
   */
  @Transactional()
  async updateBasicInfo(
    agentId: string,
    userId: number,
    updateDto: UpdateBasicInfoDto,
  ): Promise<BasicInfoResponseDto & { avatarUpload?: { uploadUrl: string; publicUrl: string } }> {
    try {
      // Kiểm tra agent có thuộc về user không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agent, agentUser } = result;

      // Prepare update data for agents table
      const agentUpdateData: any = {};
      let avatarUploadInfo: { uploadUrl: string; publicUrl: string } | null = null;

      // Handle name update
      if (updateDto.name !== undefined) {
        agentUpdateData.name = updateDto.name;
      }

      // Handle avatar upload
      if (updateDto.avatarFile) {
        try {
          const { fileName, mimeType } = updateDto.avatarFile;

          const s3Key = generateS3Key({
            baseFolder: userId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT,
            fileName: fileName,
            useTimeFolder: false
          });

          // TODO: Implement upload URL generation with S3Service
          // const uploadUrl = await this.s3Service.createPresignedUrl(s3Key, mimeType);
          const uploadUrl = `https://upload-placeholder.com/${s3Key}`;
          const publicUrl = `https://cdn-placeholder.com/${s3Key}`;

          agentUpdateData.avatar = s3Key;
          avatarUploadInfo = { uploadUrl, publicUrl };
        } catch (error) {
          this.logger.error(`Lỗi khi tạo S3 key cho avatar: ${error.message}`);
          throw new AppException(AGENT_ERROR_CODES.INVALID_S3_KEY, 'Không thể tạo URL upload cho avatar');
        }
      }

      // Handle model config update
      if (updateDto.modelConfig) {
        if (!BasicInfoMapper.validateModelConfig(updateDto.modelConfig)) {
          throw new AppException(AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG, 'Cấu hình model không hợp lệ');
        }
        
        agentUpdateData.modelConfig = BasicInfoMapper.mergeModelConfig(
          agent.modelConfig,
          updateDto.modelConfig
        );
      }

      // Handle instruction update
      if (updateDto.instruction !== undefined) {
        agentUpdateData.instruction = updateDto.instruction;
      }

      // Handle vector store update
      if (updateDto.vectorStoreId !== undefined) {
        agentUpdateData.vectorStoreId = updateDto.vectorStoreId;
      }

      // Update agents table if there are changes
      if (Object.keys(agentUpdateData).length > 0) {
        await this.agentRepository.createQueryBuilder()
          .update()
          .set(agentUpdateData)
          .where('id = :id', { id: agentId })
          .execute();
      }

      // Prepare update data for agents_user table
      const agentUserUpdateData: any = {};

      // Handle model updates (only one should be set, others should be null)
      if (updateDto.userModelId !== undefined) {
        agentUserUpdateData.userModelId = updateDto.userModelId;
        agentUserUpdateData.systemModelId = null;
        agentUserUpdateData.modelFineTuneId = null;
      } else if (updateDto.systemModelId !== undefined) {
        agentUserUpdateData.systemModelId = updateDto.systemModelId;
        agentUserUpdateData.userModelId = null;
        agentUserUpdateData.modelFineTuneId = null;
      } else if (updateDto.modelFineTuneId !== undefined) {
        agentUserUpdateData.modelFineTuneId = updateDto.modelFineTuneId;
        agentUserUpdateData.userModelId = null;
        agentUserUpdateData.systemModelId = null;
      }

      // Update agents_user table if there are changes
      if (Object.keys(agentUserUpdateData).length > 0) {
        await this.agentUserRepository.createQueryBuilder()
          .update()
          .set(agentUserUpdateData)
          .where('id = :id', { id: agentId })
          .andWhere('userId = :userId', { userId })
          .execute();
      }

      // Get updated agent info
      const updatedResult = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!updatedResult) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Resolve updated model info (simplified)
      let updatedResolvedModelId = '';
      let updatedResolvedProvider = 'unknown';

      if (updatedResult.agentUser.userModelId) {
        updatedResolvedModelId = 'user-model-placeholder';
        updatedResolvedProvider = 'user';
      } else if (updatedResult.agentUser.systemModelId) {
        updatedResolvedModelId = 'system-model-placeholder';
        updatedResolvedProvider = 'system';
      } else if (updatedResult.agentUser.modelFineTuneId) {
        updatedResolvedModelId = 'fine-tune-model-placeholder';
        updatedResolvedProvider = 'fine_tune';
      }

      // Create response
      const response = BasicInfoMapper.toResponseDto(
        updatedResult.agent,
        updatedResolvedModelId,
        updatedResolvedProvider,
        undefined, // TODO: Get actual key LLM ID
        this.cdnService
      );

      const result_with_upload = {
        ...response,
        ...(avatarUploadInfo && { avatarUpload: avatarUploadInfo })
      };

      this.logger.log(`Cập nhật basic info thành công cho agent ${agentId}`);
      return result_with_upload;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật basic info agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }
}
