import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { Agent } from '@modules/agent/entities';
import { Logger } from '@nestjs/common';
import { BasicInfoResponseDto } from '../dto/basic-info';
import { ModelConfigMapper } from './model-config.mapper';

/**
 * Mapper cho Basic Info operations
 */
export class BasicInfoMapper {
  private static readonly logger = new Logger(BasicInfoMapper.name);

  /**
   * Chuyển đổi Agent entity và resolved model info sang BasicInfoResponseDto
   * @param agent Agent entity
   * @param resolvedModelId Model ID đã resolve
   * @param resolvedProvider Provider đã resolve
   * @param keyLlmId Key LLM ID (nếu có)
   * @param cdnService CDN service để tạo avatar URL
   * @returns BasicInfoResponseDto
   */
  static toResponseDto(
    agent: Agent,
    resolvedModelId: string,
    resolvedProvider: string,
    keyLlmId?: string,
    cdnService?: CdnService
  ): BasicInfoResponseDto {
    // Tạo URL CDN cho avatar nếu có
    let avatarUrl: string | undefined;
    if (agent.avatar && cdnService) {
      try {
        avatarUrl = cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY) || undefined;
      } catch (error) {
        this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
        avatarUrl = undefined;
      }
    }

    // Chuyển đổi ModelConfig sang DTO
    const modelConfigDto = ModelConfigMapper.toDto(agent.modelConfig);

    // Map provider names
    const providerDisplayName = this.mapProviderName(resolvedProvider);

    return {
      avatar: avatarUrl || null,
      name: agent.name,
      provider: providerDisplayName,
      keyLlm: keyLlmId,
      modelId: resolvedModelId || '',
      modelConfig: modelConfigDto,
      instruction: agent.instruction || undefined,
      vectorId: agent.vectorStoreId || undefined,
      updatedAt: agent.updatedAt,
    };
  }

  /**
   * Map provider internal names to display names
   * @param provider Internal provider name
   * @returns Display provider name
   */
  private static mapProviderName(provider: string): string {
    const providerMap: Record<string, string> = {
      'user': 'USER_KEY',
      'system': 'SYSTEM_KEY',
      'fine_tune': 'FINE_TUNED',
      'unknown': 'UNKNOWN',
      // Direct provider names
      'OPENAI': 'OPENAI',
      'ANTHROPIC': 'ANTHROPIC',
      'GOOGLE': 'GOOGLE',
      'OLLAMA': 'OLLAMA',
    };

    return providerMap[provider] || provider.toUpperCase();
  }

  /**
   * Validate model configuration data
   * @param modelConfig Model config to validate
   * @returns boolean
   */
  static validateModelConfig(modelConfig: any): boolean {
    if (!modelConfig || typeof modelConfig !== 'object') {
      return false;
    }

    // Check for valid numeric ranges
    if (modelConfig.temperature !== undefined) {
      if (typeof modelConfig.temperature !== 'number' ||
        modelConfig.temperature < 0 ||
        modelConfig.temperature > 2) {
        return false;
      }
    }

    if (modelConfig.top_p !== undefined) {
      if (typeof modelConfig.top_p !== 'number' ||
        modelConfig.top_p < 0 ||
        modelConfig.top_p > 1) {
        return false;
      }
    }

    if (modelConfig.top_k !== undefined) {
      if (typeof modelConfig.top_k !== 'number' ||
        modelConfig.top_k < 0) {
        return false;
      }
    }

    if (modelConfig.max_tokens !== undefined) {
      if (typeof modelConfig.max_tokens !== 'number' ||
        modelConfig.max_tokens < 1) {
        return false;
      }
    }

    return true;
  }

  /**
   * Create default model configuration
   * @returns Default ModelConfig
   */
  static createDefaultModelConfig() {
    return {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 1000,
    };
  }

  /**
   * Merge model configuration with defaults
   * @param currentConfig Current model config
   * @param updateConfig Update model config
   * @returns Merged model config
   */
  static mergeModelConfig(currentConfig: any, updateConfig: any) {
    const defaultConfig = this.createDefaultModelConfig();

    return {
      temperature: updateConfig.temperature ?? currentConfig?.temperature ?? defaultConfig.temperature,
      top_p: updateConfig.top_p ?? currentConfig?.top_p ?? defaultConfig.top_p,
      top_k: updateConfig.top_k ?? currentConfig?.top_k ?? defaultConfig.top_k,
      max_tokens: updateConfig.max_tokens ?? currentConfig?.max_tokens ?? defaultConfig.max_tokens,
    };
  }
}
