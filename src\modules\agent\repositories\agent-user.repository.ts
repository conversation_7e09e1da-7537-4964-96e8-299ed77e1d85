import { AppException } from '@common/exceptions';
import { Agent, AgentUser } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho AgentUser
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent của người dùng
 */
@Injectable()
export class AgentUserRepository extends Repository<AgentUser> {
  private readonly logger = new Logger(AgentUserRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentUser, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentUser
   * @returns SelectQueryBuilder cho AgentUser
   */
  createBaseQuery(): SelectQueryBuilder<AgentUser> {
    return this.createQueryBuilder('agentUser');
  }

  /**
   * Tạo query builder cơ bản cho Agent
   * @returns SelectQueryBuilder cho Agent
   */
  private createAgentBaseQuery(): SelectQueryBuilder<Agent> {
    return this.dataSource
      .getRepository(Agent)
      .createQueryBuilder('agent');
  }

  /**
   * Tìm agent của người dùng theo ID
   * @param id ID của agent
   * @returns AgentUser nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentUser | null> {
    return this.createBaseQuery()
      .where('agentUser.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm agent theo ID và userId
   * @param id ID của agent
   * @param userId ID của người dùng
   * @returns Agent và AgentUser nếu tìm thấy
   */
  async findAgentByIdAndUserId(
    id: string,
    userId: number,
  ): Promise<{ agent: Agent; agentUser: AgentUser } | null> {
    // Select tất cả các trường cần thiết từ agent, bao gồm modelConfig
    const agent = await this.createAgentBaseQuery()
      .select([
        'agent.id',
        'agent.name',
        'agent.avatar',
        'agent.modelConfig',
        'agent.instruction',
        'agent.status',
        'agent.vectorStoreId',
        'agent.createdAt',
        'agent.updatedAt'
      ])
      .where('agent.id = :id', { id })
      .andWhere('agent.deletedAt IS NULL')
      .getOne();

    if (!agent) {
      return null;
    }

    // Select tất cả các trường cần thiết từ agentUser với JOIN model tables (theo logic mới)
    const agentUserQuery = this.createBaseQuery()
      .leftJoinAndSelect('agentUser.typeAgent', 'typeAgent')
      .leftJoin('user_models', 'userModel', 'agentUser.user_model_id = userModel.id')
      .leftJoin('system_models', 'systemModel', 'agentUser.system_model_id = systemModel.id')
      .leftJoin('user_model_fine_tune', 'userModelFineTune', 'agentUser.model_fine_tune_id = userModelFineTune.id')
      .addSelect('agentUser.id', 'agentUser_id')
      .addSelect('agentUser.userId', 'agentUser_userId')
      .addSelect('agentUser.typeId', 'agentUser_typeId')
      .addSelect('agentUser.active', 'agentUser_active')
      .addSelect('agentUser.exp', 'agentUser_exp')
      .addSelect('agentUser.strategyId', 'agentUser_strategyId')
      .addSelect('agentUser.profile', 'agentUser_profile')
      .addSelect('agentUser.convertConfig', 'agentUser_convertConfig')
      .addSelect('agentUser.userModelId', 'agentUser_userModelId')
      .addSelect('agentUser.systemModelId', 'agentUser_systemModelId')
      .addSelect('agentUser.modelFineTuneId', 'agentUser_modelFineTuneId')
      // Lấy model_id từ các bảng JOIN (theo logic mới)
      .addSelect('userModel.modelId', 'userModel_modelId')
      .addSelect('systemModel.modelId', 'systemModel_modelId')
      .addSelect('userModelFineTune.modelId', 'userModelFineTune_modelId')
      .where('agentUser.id = :id', { id })
      .andWhere('agentUser.userId = :userId', { userId });

    const rawResult = await agentUserQuery.getRawAndEntities();
    const agentUser = rawResult.entities[0];
    const rawData = rawResult.raw[0];

    if (!agentUser) {
      return null;
    }

    // Resolve model_id từ raw data
    let resolvedModelId = '';
    let resolvedProvider = '';

    // Debug logging để kiểm tra dữ liệu từ raw data
    this.logger.debug(`Debug model resolution for agent ${id}:`, {
      // Từ raw data
      agentUser_userModelId: rawData?.agentUser_userModelId,
      agentUser_systemModelId: rawData?.agentUser_systemModelId,
      agentUser_modelFineTuneId: rawData?.agentUser_modelFineTuneId,
      userModel_modelId: rawData?.userModel_modelId,
      systemModel_modelId: rawData?.systemModel_modelId,
      userModelFineTune_modelId: rawData?.userModelFineTune_modelId,
    });

    // Sử dụng raw data thay vì entity vì entity không được populate đúng
    if (rawData?.agentUser_userModelId && rawData?.userModel_modelId) {
      // Scenario 1: user_model_id != null → JOIN user_models → lấy user_models.model_id
      resolvedModelId = rawData.userModel_modelId;
      resolvedProvider = 'user';
      this.logger.debug(`Using Scenario 1 (user model): ${resolvedModelId}`);
    } else if (rawData?.agentUser_systemModelId && rawData?.systemModel_modelId) {
      // Scenario 2: system_model_id != null → JOIN system_models → lấy system_models.model_id
      resolvedModelId = rawData.systemModel_modelId;
      resolvedProvider = 'system';
      this.logger.debug(`Using Scenario 2 (system model): ${resolvedModelId}`);
    } else if (rawData?.agentUser_modelFineTuneId && rawData?.userModelFineTune_modelId) {
      // Scenario 3: model_fine_tune_id != null → JOIN user_model_fine_tune → lấy user_model_fine_tune.model_id
      resolvedModelId = rawData.userModelFineTune_modelId;
      resolvedProvider = 'fine_tune';
      this.logger.debug(`Using Scenario 3 (fine tune model): ${resolvedModelId}`);
    } else {
      // Scenario 4: Không có model nào → trống
      resolvedModelId = '';
      resolvedProvider = 'unknown';
      this.logger.debug(`Using Scenario 4 (no model): empty`);
    }

    // Populate entity fields từ raw data để service có thể truy cập
    agentUser.userModelId = rawData?.agentUser_userModelId || null;
    agentUser.systemModelId = rawData?.agentUser_systemModelId || null;
    agentUser.modelFineTuneId = rawData?.agentUser_modelFineTuneId || null;

    // Thêm resolved model info vào agentUser
    (agentUser as any).resolvedModelId = resolvedModelId;
    (agentUser as any).resolvedProvider = resolvedProvider;

    return { agent, agentUser };
  }

  /**
   * Tìm danh sách agent có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent có phân trang
   */
  // async findPaginated(
  //   userId: number,
  //   queryDto: AgentQueryDto,
  // ): Promise<PaginatedResult<any>> {
  //   try {
  //     // Tạo query builder với JOIN đến các bảng model (theo logic mới)
  //     const qb = this.createAgentBaseQuery()
  //       .innerJoin('agents_user', 'agentUser', 'agentUser.id = agent.id')
  //       .leftJoin('type_agents', 'typeAgent', 'agentUser.type_id = typeAgent.id')
  //       .leftJoin('user_models', 'userModel', 'agentUser.user_model_id = userModel.id')
  //       .leftJoin('system_models', 'systemModel', 'agentUser.system_model_id = systemModel.id')
  //       .leftJoin('user_model_fine_tune', 'userModelFineTune', 'agentUser.model_fine_tune_id = userModelFineTune.id')
  //       .where('agentUser.user_id = :userId', { userId })
  //       .andWhere('agent.deleted_at IS NULL');

  //     // Thêm điều kiện tìm kiếm nếu có
  //     if (queryDto.search) {
  //       qb.andWhere('agent.name ILIKE :search', { search: `%${queryDto.search}%` });
  //     }

  //     // Thêm điều kiện lọc theo loại agent nếu có
  //     if (queryDto.typeId) {
  //       qb.andWhere('agentUser.type_id = :typeId', { typeId: queryDto.typeId });
  //     }

  //     // Chỉ hiển thị các agent có trạng thái là PENDING hoặc APPROVED
  //     qb.andWhere('agent.status IN (:...statuses)', {
  //       statuses: [AgentStatusEnum.PENDING, AgentStatusEnum.APPROVED]
  //     });

  //     // Thêm điều kiện lọc theo trạng thái hoạt động nếu có
  //     if (queryDto.active !== undefined) {
  //       qb.andWhere('agentUser.active = :active', { active: queryDto.active });
  //     }

  //     // Thêm phân trang và sắp xếp
  //     qb.skip((queryDto.page - 1) * queryDto.limit)
  //       .take(queryDto.limit)
  //       .orderBy(`agent.${queryDto.sortBy}`, queryDto.sortDirection);

  //     // Lấy kết quả với các trường cần thiết, bao gồm resolved model_id từ JOIN
  //     const queryBuilder = qb
  //       .addSelect('agent.id', 'agent_id')
  //       .addSelect('agent.name', 'agent_name')
  //       .addSelect('agent.avatar', 'agent_avatar')
  //       .addSelect('agent.createdAt', 'agent_createdAt')
  //       .addSelect('agent.updatedAt', 'agent_updatedAt')
  //       .addSelect('agent.modelConfig', 'agent_model_config')
  //       .addSelect('agentUser.typeId', 'agentUser_type_id')
  //       .addSelect('agentUser.active', 'agentUser_active')
  //       .addSelect('agentUser.exp', 'agentUser_exp')
  //       .addSelect('agentUser.userModelId', 'agentUser_user_model_id')
  //       .addSelect('agentUser.systemModelId', 'agentUser_system_model_id')
  //       .addSelect('agentUser.modelFineTuneId', 'agentUser_model_fine_tune_id')
  //       .addSelect('typeAgent.name', 'typeAgent_name')
  //       // Lấy model_id từ các bảng đã JOIN (theo logic mới)
  //       .addSelect('userModel.modelId', 'userModel_model_id')
  //       .addSelect('systemModel.modelId', 'systemModel_model_id')
  //       .addSelect('userModelFineTune.modelId', 'userModelFineTune_model_id');

  //     // Thực hiện truy vấn SQL trực tiếp để lấy cả typeName
  //     const rawResults = await queryBuilder.getRawMany();
  //     const total = await queryBuilder.getCount();

  //     // Chuyển đổi kết quả raw thành định dạng cần thiết với resolved model_id
  //     const items = rawResults.map(item => {
  //       // Resolve model_id từ 4 scenarios
  //       let resolvedModelId = '';
  //       let resolvedProvider = '';

  //       if (item.agentUser_user_model_id && item.userModel_model_id) {
  //         // Scenario 1: user_model_id != null → JOIN user_models → lấy user_models.model_id
  //         resolvedModelId = item.userModel_model_id;
  //         resolvedProvider = 'user';
  //       } else if (item.agentUser_system_model_id && item.systemModel_model_id) {
  //         // Scenario 2: system_model_id != null → JOIN system_models → lấy system_models.model_id
  //         resolvedModelId = item.systemModel_model_id;
  //         resolvedProvider = 'system';
  //       } else if (item.agentUser_model_fine_tune_id && item.userModelFineTune_model_id) {
  //         // Scenario 3: model_fine_tune_id != null → JOIN user_model_fine_tune → lấy user_model_fine_tune.model_id
  //         resolvedModelId = item.userModelFineTune_model_id;
  //         resolvedProvider = 'fine_tune';
  //       } else {
  //         // Scenario 4: Không có model nào → trống
  //         resolvedModelId = '';
  //         resolvedProvider = 'unknown';
  //       }

  //       return {
  //         id: item.agent_id,
  //         name: item.agent_name,
  //         avatar: item.agent_avatar,
  //         typeId: item.agentUser_type_id,
  //         typeName: item.typeAgent_name,
  //         active: item.agentUser_active,
  //         createdAt: item.agent_createdAt,
  //         updatedAt: item.agent_updatedAt,
  //         exp: item.agentUser_exp || 0,
  //         modelConfig: item.agent_model_config || null,
  //         // Model fields từ agents_user
  //         userModelId: item.agentUser_user_model_id,
  //         systemModelId: item.agentUser_system_model_id,
  //         modelFineTuneId: item.agentUser_model_fine_tune_id,
  //         // Resolved model information
  //         resolvedModelId,
  //         resolvedProvider,
  //       };
  //     });

  //     return {
  //       items,
  //       meta: {
  //         totalItems: total,
  //         itemCount: items.length,
  //         itemsPerPage: queryDto.limit,
  //         totalPages: Math.ceil(total / queryDto.limit),
  //         currentPage: queryDto.page,
  //       },
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi truy vấn danh sách agent: ${error.message}`);
  //     throw error;
  //   }
  // }

  /**
   * Cập nhật trạng thái hoạt động của agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param active Trạng thái hoạt động mới
   */
  @Transactional()
  async updateAgentActive(
    id: string,
    userId: number,
    active: boolean,
  ): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không - chỉ select các trường cần thiết
      const agentUser = await this.createBaseQuery()
        .select(['agentUser.id', 'agentUser.userId'])
        .where('agentUser.id = :id', { id })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();

      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Cập nhật trạng thái hoạt động
      const qb = this.createQueryBuilder()
        .update(AgentUser)
        .set({ active })
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái hoạt động của agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật loại agent cho một agent
   * @param id ID của agent
   * @param userId ID của người dùng
   * @param typeId ID loại agent mới
   */
  @Transactional()
  async updateAgentType(
    id: string,
    userId: number,
    typeId: number,
  ): Promise<void> {
    try {
      // Cập nhật loại agent
      const qb = this.createQueryBuilder()
        .update(AgentUser)
        .set({ typeId })
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật loại agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa mềm agent
   * @param id ID của agent
   * @param userId ID của người dùng
   */
  @Transactional()
  async softDeleteAgent(id: string, userId: number): Promise<void> {
    try {
      // Kiểm tra agent có tồn tại không - chỉ select các trường cần thiết
      const agentExists = await this.createBaseQuery()
        .select(['agentUser.id'])
        .where('agentUser.id = :id', { id })
        .andWhere('agentUser.userId = :userId', { userId })
        .getOne();

      if (!agentExists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Xóa mềm agent
      const qb = this.dataSource
        .getRepository(Agent)
        .createQueryBuilder()
        .update(Agent)
        .set({ deletedAt: Date.now() })
        .where('id = :id', { id });

      const result = await qb.execute();
      if (result.affected === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa agent: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật Strategy ID cho agent user
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param strategyId ID của strategy
   */
  async updateStrategyId(agentId: string, userId: number, strategyId: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentUser)
        .set({ strategyId })
        .where('id = :agentId', { agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      if (result.affected === 0) {
        this.logger.warn(`Không tìm thấy agent user ${agentId} cho user ${userId} để cập nhật strategy`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật strategy cho agent user ${agentId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
