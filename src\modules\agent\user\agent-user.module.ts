import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { MediaModule } from '@modules/data/media/media.module';
import { UrlModule } from '@modules/data/url/url.module';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { S3Service } from '@shared/services/s3.service';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { ToolsUserModule } from '@/modules/tools/user/tools-user.module';

import { BusinessUserModule } from '@modules/business/user/business-user.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { UserWebsiteRepository, FacebookPageRepository } from '@modules/integration/repositories';
import {
  Agent,
  AgentMedia,
  AgentProduct,
  AgentUrl,
  AgentUser,
  TypeAgent,
  UserMultiAgent
} from '@modules/agent/entities';
import {
  AgentRepository,
  AgentUserRepository,
  TypeAgentRepository,
  AgentMediaRepository,
  AgentProductRepository,
  AgentUrlRepository,
  AgentRankRepository,
  UserMultiAgentRepository
} from '@modules/agent/repositories';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import {
  TypeAgentUserController,
  AgentResourceUserController,
  MultiAgentUserController,
  AgentStrategyUserController,
  AgentFacebookPageController,
  AgentWebsiteController,
  AgentUserController
} from './controllers';
import {
  AgentUserService,
  AgentResourceUserService,
  MultiAgentUserService,
  AgentStrategyService,
  AgentFacebookPageService,
  AgentWebsiteService
} from './services';
import { TypeAgentUserService } from './services/type-agent-user.service';
import { TypeAgentValidationHelper } from './helpers/type-agent-validation.helper';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([
      UserProduct,
      Agent,
      AgentUser,
      AgentMedia,
      AgentUrl,
      AgentProduct,
      TypeAgent,
      UserMultiAgent,
    ]),
    HttpModule,
    MediaModule,
    UrlModule,
    MarketplaceModule,
    ToolsModule,
    ToolsUserModule,
    KnowledgeFilesModule,
    ConfigModule,
    BusinessUserModule,
    IntegrationModule,
  ],
  controllers: [
    TypeAgentUserController,
    AgentResourceUserController,
    MultiAgentUserController,
    AgentStrategyUserController,
    AgentFacebookPageController,
    AgentWebsiteController,
    AgentUserController,
  ],
  providers: [
    // Services
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    MultiAgentUserService,
    AgentStrategyService,
    AgentFacebookPageService,
    AgentWebsiteService,

    // External services
    OpenAiService,
    S3Service,
    FacebookService,
    RagFileProcessingService,

    // Helpers
    TypeAgentValidationHelper,

    // Repositories
    TypeAgentRepository,
    AgentRepository,
    AgentUserRepository,
    AgentMediaRepository,
    AgentProductRepository,
    AgentUrlRepository,
    VectorStoreRepository,
    MediaRepository,
    UrlRepository,
    UserWebsiteRepository,
    FacebookPageRepository,
    AgentRankRepository,
    UserMultiAgentRepository,
  ],
  exports: [
    AgentUserService,
    AgentResourceUserService,
    AgentStrategyService,
  ],
})
export class AgentUserModule {}
