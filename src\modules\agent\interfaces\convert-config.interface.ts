/**
 * Interface cho cấu hình chuyển đổi dữ liệu
 */
export interface ConvertConfig {
  /**
   * Tên của field trong schema JSON
   */
  name: string;

  /**
   * Kiểu dữ liệu của field
   */
  type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | "enum";

  /**
   * <PERSON><PERSON>ả (nội dung) của field
   */
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  required: boolean;

  /**
   * Giá trị mặc định
   */
  defaultValue?: any;
}