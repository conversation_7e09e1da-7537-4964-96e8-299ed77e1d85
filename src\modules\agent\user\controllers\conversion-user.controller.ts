import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Post,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiResponseDto } from '@common/response';
import { ErrorCode } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { ConversionUserService } from '../services/conversion-user.service';
import {
  UpdateConversionDto,
  ConversionResponseDto,
} from '../dto/conversion';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { CurrentUser } from '@/modules/auth/decorators';

/**
 * Controller xử lý các API liên quan đến conversion config của agent cho người dùng
 */
@ApiTags('User Agent Conversion')
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ConversionUserController {
  constructor(private readonly conversionUserService: ConversionUserService) { }

  // ==================== CONVERSION ENDPOINTS ====================

  /**
   * Lấy thông tin conversion config của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin conversion config của agent
   */
  @Get(':id/conversion')
  @ApiOperation({ summary: 'Lấy thông tin conversion config của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin conversion config thành công',
    type: ConversionResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getConversion(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<ConversionResponseDto>> {
    const result = await this.conversionUserService.getConversion(id, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin conversion config thành công');
  }

  /**
   * Cập nhật conversion config của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param updateDto Thông tin conversion config cần cập nhật
   * @returns Thông tin conversion config đã cập nhật
   */
  @Put(':id/conversion')
  @ApiOperation({ summary: 'Cập nhật conversion config của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật conversion config thành công',
    type: ConversionResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateConversion(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateConversionDto,
  ): Promise<ApiResponseDto<ConversionResponseDto>> {
    const result = await this.conversionUserService.updateConversion(id, userId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật conversion config thành công');
  }

  /**
   * Reset conversion config về mặc định
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin conversion config mặc định
   */
  @Post(':id/conversion/reset')
  @ApiOperation({ summary: 'Reset conversion config về mặc định' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Reset conversion config thành công',
    type: ConversionResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async resetConversion(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<ConversionResponseDto>> {
    const result = await this.conversionUserService.resetConversion(id, userId);
    return ApiResponseDto.success(result, 'Reset conversion config thành công');
  }
}
