import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ModelConfigDto } from '../model-config.dto';

/**
 * DTO cho response thông tin cơ bản của agent
 */
export class BasicInfoResponseDto {
  /**
   * URL avatar của agent
   */
  @ApiProperty({
    description: 'URL avatar của agent',
    example: 'https://cdn.example.com/avatars/agent-avatar.jpg',
    nullable: true,
  })
  avatar: string | null;

  /**
   * Tên agent
   */
  @ApiProperty({
    description: 'Tên agent',
    example: 'AI Assistant Marketing',
  })
  name: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    example: 'OPENAI',
    enum: ['OPENAI', 'ANTHROPIC', 'GOOGLE', 'OLLAMA', 'USER_KEY', 'SYSTEM_KEY', 'FINE_TUNED', 'UNKNOWN'],
  })
  provider: string;

  /**
   * ID của key LLM
   */
  @ApiPropertyOptional({
    description: 'ID của key LLM được sử dụng',
    example: 'key-llm-uuid-123',
  })
  keyLlm?: string;

  /**
   * ID của model
   */
  @ApiProperty({
    description: 'ID của model được sử dụng',
    example: 'gpt-4o',
  })
  modelId: string;

  /**
   * Cấu hình model
   */
  @ApiProperty({
    description: 'Cấu hình model (temperature, top_p, etc.)',
    type: ModelConfigDto,
  })
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn/System prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý AI chuyên về marketing. Hãy giúp người dùng tạo nội dung marketing hiệu quả.',
  })
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store được sử dụng',
    example: 'vector-store-uuid-123',
  })
  vectorId?: string;

  /**
   * Thông tin upload avatar (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Thông tin upload avatar (nếu có)',
    type: 'object',
    properties: {
      uploadUrl: {
        type: 'string',
        description: 'URL để upload avatar',
        example: 'https://s3.amazonaws.com/bucket/upload-url'
      },
      publicUrl: {
        type: 'string',
        description: 'URL public của avatar sau khi upload',
        example: 'https://s3.amazonaws.com/bucket/avatar.jpg'
      }
    },
  })
  avatarUpload?: {
    uploadUrl: string;
    publicUrl: string;
  };

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật gần nhất (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;
}
