import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsString, 
  IsEnum, 
  IsOptional, 
  IsBoolean,
  MaxLength
} from 'class-validator';

/**
 * Enum cho các kiểu dữ liệu của field trong conversion config
 */
export enum ConvertFieldType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
}

/**
 * DTO cho một item trong conversion config
 */
export class ConvertConfigItemDto {
  /**
   * Tên của field trong schema JSON
   */
  @ApiProperty({
    description: 'Tên của field trong schema JSON',
    example: 'customer_name',
    maxLength: 100,
  })
  @IsString({ message: 'Tên field phải là chuỗi' })
  @MaxLength(100, { message: 'Tên field không được vượt quá 100 ký tự' })
  name: string;

  /**
   * <PERSON>ểu dữ liệu của field
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu của field',
    example: 'string',
    enum: ConvertFieldType,
  })
  @IsEnum(ConvertFieldType, { message: 'Kiểu dữ liệu phải là một trong các giá trị hợp lệ' })
  type: ConvertFieldType;

  /**
   * Mô tả (nội dung) của field
   */
  @ApiPropertyOptional({
    description: 'Mô tả hoặc nội dung của field',
    example: 'Tên đầy đủ của khách hàng',
    maxLength: 500,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả không được vượt quá 500 ký tự' })
  description?: string;

  /**
   * Trường này có bắt buộc không?
   */
  @ApiPropertyOptional({
    description: 'Trường này có bắt buộc không?',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Required phải là boolean' })
  @Type(() => Boolean)
  required?: boolean;

  /**
   * Giá trị mặc định
   */
  @ApiPropertyOptional({
    description: 'Giá trị mặc định cho field',
    example: 'Khách hàng mới',
  })
  @IsOptional()
  defaultValue?: any;
}
