import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Entity quản lý media của agent
 * Bảng: agent_media
 */
@Entity('agent_media')
export class AgentMedia {
  /**
   * ID chính của media (auto increment)
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * ID của agent sở hữu media
   */
  @Column({
    type: 'integer',
    name: 'agent_id',
    comment: 'ID của agent sở hữu media'
  })
  agentId: number;

  /**
   * ID của media
   */
  @Column({
    type: 'integer',
    name: 'media_id',
    comment: 'ID của media'
  })
  mediaId: number;

  /**
   * Loại media (image, video, audio, document)
   */
  @Column({
    type: 'varchar',
    length: 50,
    name: 'media_type',
    comment: 'Loại media (image, video, audio, document)'
  })
  mediaType: string;

  /**
   * Đường dẫn file media
   */
  @Column({
    type: 'varchar',
    length: 500,
    name: 'file_path',
    comment: 'Đường dẫn file media'
  })
  filePath: string;

  /**
   * Tên file gốc
   */
  @Column({
    type: 'varchar',
    length: 255,
    name: 'original_name',
    comment: 'Tên file gốc'
  })
  originalName: string;

  /**
   * Kích thước file (bytes)
   */
  @Column({
    type: 'bigint',
    name: 'file_size',
    comment: 'Kích thước file (bytes)'
  })
  fileSize: number;

  /**
   * MIME type của file
   */
  @Column({
    type: 'varchar',
    length: 100,
    name: 'mime_type',
    comment: 'MIME type của file'
  })
  mimeType: string;

  /**
   * Trạng thái hoạt động
   */
  @Column({
    type: 'boolean',
    default: true,
    comment: 'Trạng thái hoạt động'
  })
  active: boolean;

  /**
   * Thời gian tạo
   */
  @CreateDateColumn({
    type: 'timestamp',
    name: 'created_at',
    comment: 'Thời gian tạo'
  })
  createdAt: Date;

  /**
   * Thời gian cập nhật lần cuối
   */
  @UpdateDateColumn({
    type: 'timestamp',
    name: 'updated_at',
    comment: 'Thời gian cập nhật lần cuối'
  })
  updatedAt: Date;
}
