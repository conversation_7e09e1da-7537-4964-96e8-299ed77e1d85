import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';

/**
 * Entity đại diện cho bảng media_data trong cơ sở dữ liệu
 * Bảng lưu thông tin tài nguyên media
 */
@Entity('media_data')
export class Media {
  /**
   * Mã định danh media
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * Tên media
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * Mô tả về tài nguyên media
   */
  @Column({ name: 'description', type: 'text' })
  description: string;

  /**
   * Kích thước media (byte)
   */
  @Column({ name: 'size', type: 'bigint' })
  size: number;

  /**
   * Các thẻ phân loại media (dạng JSONB)
   */
  @Column({ name: 'tags', type: 'jsonb', nullable: true })
  tags: any;

  /**
   * <PERSON>h<PERSON><PERSON> định danh trên hệ thống lưu trữ
   */
  @Column({ name: 'storage_key', length: 512, unique: true })
  storageKey: string;

  /**
   * Mã người sở hữu media (FK tới users)
   */
  @Column({ name: 'owned_by' })
  ownedBy: number;

  /**
   * Thời điểm tạo bản ghi (unix timestamp)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật bản ghi (unix timestamp)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Vector embedding cho tên media (1536d)
   */
  @Column({ name: 'name_embedding', type: 'simple-array', nullable: true })
  nameEmbedding: number[];

  /**
   * Vector embedding cho mô tả media (1536d)
   */
  @Column({
    name: 'description_embedding',
    type: 'simple-array',
    nullable: true,
  })
  descriptionEmbedding: number[];

  /**
   * Trạng thái của file: PENDING - đang chờ duyệt, APPROVED - đã được duyệt, REJECTED - bị từ chối, DISABLED - đã bị vô hiệu hóa
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: MediaStatusEnum,
    default: MediaStatusEnum.DRAFT,
  })
  status: MediaStatusEnum;
}
