import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { 
  AgentUserRepository
} from '@modules/agent/repositories';
import { Transactional } from 'typeorm-transactional';
import { 
  UpdateProfileDto,
  ProfileResponseDto
} from '../dto/profile';
import { ProfileMapper } from '../mappers';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';

/**
 * Service xử lý các thao tác liên quan đến profile của agent cho người dùng
 */
@Injectable()
export class ProfileUserService {
  private readonly logger = new Logger(ProfileUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
  ) { }

  /**
   * Lấy thông tin profile của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin profile
   */
  async getProfile(
    agentId: string,
    userId: number,
  ): Promise<ProfileResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agentUser } = result;

      // Chuyển đổi ProfileAgent sang ProfileDto
      const profileDto = ProfileMapper.toDto(agentUser.profile);

      // Tạo response với updatedAt
      const response: ProfileResponseDto = {
        ...profileDto,
        updatedAt: Date.now(), // Sử dụng timestamp hiện tại
      };

      this.logger.log(`Lấy profile thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy profile agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật profile của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin profile cần cập nhật
   * @returns Thông tin profile đã cập nhật
   */
  @Transactional()
  async updateProfile(
    agentId: string,
    userId: number,
    updateDto: UpdateProfileDto,
  ): Promise<ProfileResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không
      const agentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!agentUser) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Chuyển đổi UpdateProfileDto sang ProfileAgent
      const currentProfile = agentUser.profile || {};
      const updatedProfile = this.mergeProfileData(currentProfile, updateDto);

      // Cập nhật profile trong database
      await this.agentUserRepository.createQueryBuilder()
        .update()
        .set({ 
          profile: updatedProfile,
          updatedAt: Date.now()
        })
        .where('id = :id', { id: agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      // Lấy thông tin agent đã cập nhật
      const updatedAgentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      // Chuyển đổi sang response DTO
      const profileDto = ProfileMapper.toDto(updatedAgentUser.profile);
      const response: ProfileResponseDto = {
        ...profileDto,
        updatedAt: updatedAgentUser.updatedAt,
      };

      this.logger.log(`Cập nhật profile thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật profile agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== PRIVATE METHODS ====================

  /**
   * Merge profile data từ DTO với profile hiện tại
   * @param currentProfile Profile hiện tại
   * @param updateDto DTO cập nhật
   * @returns ProfileAgent đã merge
   */
  private mergeProfileData(
    currentProfile: ProfileAgent, 
    updateDto: UpdateProfileDto
  ): ProfileAgent {
    const updatedProfile: ProfileAgent = { ...currentProfile };

    // Cập nhật các trường nếu có trong DTO
    if (updateDto.gender !== undefined) {
      updatedProfile.gender = updateDto.gender as any; // Will be converted by ProfileMapper
    }

    if (updateDto.dateOfBirth !== undefined) {
      updatedProfile.dateOfBirth = new Date(updateDto.dateOfBirth);
    }

    if (updateDto.position !== undefined) {
      updatedProfile.position = updateDto.position;
    }

    if (updateDto.education !== undefined) {
      updatedProfile.education = updateDto.education;
    }

    if (updateDto.skills !== undefined) {
      updatedProfile.skills = updateDto.skills;
    }

    if (updateDto.personality !== undefined) {
      updatedProfile.personality = updateDto.personality;
    }

    if (updateDto.languages !== undefined) {
      updatedProfile.languages = updateDto.languages;
    }

    if (updateDto.nations !== undefined) {
      updatedProfile.nations = updateDto.nations;
    }

    return updatedProfile;
  }
}
