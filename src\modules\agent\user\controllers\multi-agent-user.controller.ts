import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AddMultiAgentDto,
  BulkMultiAgentOperationResponseDto,
  MultiAgentQueryDto,
  MultiAgentResponseDto,
  RemoveMultiAgentDto,
} from '../dto/multi-agent';
import { MultiAgentUserService } from '../services/multi-agent-user.service';

/**
 * Controller xử lý các API liên quan đến multi-agent system cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class MultiAgentUserController {
  constructor(private readonly multiAgentUserService: MultiAgentUserService) { }

  // ==================== MULTI-AGENT ENDPOINTS ====================

  /**
   * Lấy danh sách agent con của agent cha
   * @param userId ID của người dùng
   * @param id ID của agent cha
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent con có phân trang
   */
  @Get(':id/multi-agents')
  @ApiOperation({ summary: 'Lấy danh sách agent con của agent cha' })
  @ApiParam({ name: 'id', description: 'ID của agent cha' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách agent con thành công',
    schema: ApiResponseDto.getPaginatedSchema(MultiAgentResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.MULTI_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getMultiAgents(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: MultiAgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<MultiAgentResponseDto>>> {
    const result = await this.multiAgentUserService.getMultiAgents(id, userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Thêm agent con vào multi-agent system
   * @param userId ID của người dùng
   * @param id ID của agent cha
   * @param addDto Thông tin agent con cần thêm
   * @returns Kết quả bulk operation
   */
  @Post(':id/multi-agents')
  @ApiOperation({ summary: 'Thêm agent con vào multi-agent system' })
  @ApiParam({ name: 'id', description: 'ID của agent cha' })
  @ApiResponse({
    status: 200,
    description: 'Thêm agent con thành công',
    type: BulkMultiAgentOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.MULTI_AGENT_SELF_REFERENCE,
    AGENT_ERROR_CODES.MULTI_AGENT_CREATION_FAILED,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addMultiAgents(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addDto: AddMultiAgentDto,
  ): Promise<ApiResponseDto<BulkMultiAgentOperationResponseDto>> {
    const result = await this.multiAgentUserService.addMultiAgents(id, userId, addDto);
    return ApiResponseDto.success(result, 'Thêm agent con thành công');
  }

  /**
   * Gỡ bỏ agent con khỏi multi-agent system
   * @param userId ID của người dùng
   * @param id ID của agent cha
   * @param removeDto Thông tin agent con cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  @Delete(':id/multi-agents')
  @ApiOperation({ summary: 'Gỡ bỏ agent con khỏi multi-agent system' })
  @ApiParam({ name: 'id', description: 'ID của agent cha' })
  @ApiResponse({
    status: 200,
    description: 'Gỡ bỏ agent con thành công',
    type: BulkMultiAgentOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.MULTI_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.MULTI_AGENT_DELETE_FAILED,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeMultiAgents(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removeDto: RemoveMultiAgentDto,
  ): Promise<ApiResponseDto<BulkMultiAgentOperationResponseDto>> {
    const result = await this.multiAgentUserService.removeMultiAgents(id, userId, removeDto);
    return ApiResponseDto.success(result, 'Gỡ bỏ agent con thành công');
  }
}
